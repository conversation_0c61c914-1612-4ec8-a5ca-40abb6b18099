'use client'

import { useState } from 'react'
import { useDraftQueue } from '@/hooks/useDraftQueue'
import { formatDuration } from '@/lib/utils/format'
import { formatTime } from '@/lib/utils/time'
import { DraftVideoItem } from '@/lib/types/video'

// Component for editing individual draft item properties
function DraftItemEditor({ item, onUpdate, onRemove }: {
  item: DraftVideoItem
  onUpdate: (updates: Partial<Pick<DraftVideoItem, 'loopCount' | 'startTime' | 'endTime'>>) => void
  onRemove: () => void
}) {
  const [isEditing, setIsEditing] = useState(false)
  const [loopCount, setLoopCount] = useState(item.loopCount)
  const [startTime, setStartTime] = useState(item.startTime || 0)
  const [endTime, setEndTime] = useState(item.endTime || item.duration)

  const handleSave = () => {
    onUpdate({
      loopCount,
      startTime: startTime > 0 ? startTime : undefined,
      endTime: endTime > 0 && endTime !== item.duration ? endTime : undefined
    })
    setIsEditing(false)
  }

  const handleCancel = () => {
    setLoopCount(item.loopCount)
    setStartTime(item.startTime || 0)
    setEndTime(item.endTime || item.duration)
    setIsEditing(false)
  }



  return (
    <div className="flex items-center gap-3 p-3 bg-dark-800/30 border border-dark-600/30 rounded-lg hover:bg-dark-700/30 transition-colors">
      {/* Thumbnail */}
      <div className="relative flex-shrink-0">
        <img
          src={item.thumbnail}
          alt={item.title}
          className="w-16 h-12 rounded-lg object-cover"
          loading="lazy"
        />
        <div className="absolute -top-1 -left-1 w-5 h-5 bg-primary-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
          {item.loopCount}
        </div>
      </div>

      {/* Video Info */}
      <div className="flex-1 min-w-0">
        <div className="text-white font-medium text-sm line-clamp-2 mb-1">
          {item.title}
        </div>
        <div className="text-dark-300 text-xs mb-1">
          {formatDuration(item.duration)} • {item.channel || 'YouTube'}
        </div>
        {(item.startTime !== undefined || item.endTime !== undefined) && (
          <div className="text-primary-400 text-xs">
            {item.startTime !== undefined && `Start: ${formatTime(item.startTime)}`}
            {item.startTime !== undefined && item.endTime !== undefined && ' • '}
            {item.endTime !== undefined && `End: ${formatTime(item.endTime)}`}
          </div>
        )}
      </div>

      {/* Controls */}
      <div className="flex items-center space-x-2">
        {!isEditing ? (
          <>
            <button
              onClick={() => setIsEditing(true)}
              className="p-2 text-dark-300 hover:text-primary-400 hover:bg-primary-400/10 rounded-lg transition-colors"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
              </svg>
            </button>
            <button
              onClick={onRemove}
              className="p-2 text-dark-300 hover:text-red-400 hover:bg-red-400/10 rounded-lg transition-colors"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </button>
          </>
        ) : (
          <div className="flex flex-col gap-2 p-2 bg-dark-700/50 rounded-lg min-w-48">
            {/* Loop Count */}
            <div className="flex items-center gap-2">
              <label className="text-xs text-dark-300 w-12">Loop:</label>
              <input
                type="number"
                min="1"
                max="99"
                value={loopCount}
                onChange={(e) => setLoopCount(Math.max(1, parseInt(e.target.value) || 1))}
                className="input-field text-xs py-1 px-2 w-16"
              />
            </div>

            {/* Start Time */}
            <div className="flex items-center gap-2">
              <label className="text-xs text-dark-300 w-12">Start:</label>
              <input
                type="number"
                min="0"
                max={item.duration}
                step="1"
                value={startTime}
                onChange={(e) => setStartTime(Math.max(0, Math.min(item.duration, parseInt(e.target.value) || 0)))}
                className="input-field text-xs py-1 px-2 w-16"
              />
              <span className="text-xs text-dark-400">s</span>
            </div>

            {/* End Time */}
            <div className="flex items-center gap-2">
              <label className="text-xs text-dark-300 w-12">End:</label>
              <input
                type="number"
                min={startTime + 1}
                max={item.duration}
                step="1"
                value={endTime}
                onChange={(e) => setEndTime(Math.max(startTime + 1, Math.min(item.duration, parseInt(e.target.value) || item.duration)))}
                className="input-field text-xs py-1 px-2 w-16"
              />
              <span className="text-xs text-dark-400">s</span>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-1 mt-1">
              <button
                onClick={handleSave}
                className="btn-primary text-xs px-2 py-1 flex-1"
              >
                Save
              </button>
              <button
                onClick={handleCancel}
                className="btn-secondary text-xs px-2 py-1 flex-1"
              >
                Cancel
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export function DraftQueue() {
  const { draftItems, draftCount, removeFromDraft, updateDraftItem, clearDraft } = useDraftQueue()

  if (draftCount === 0) {
    return (
      <div className="glassmorphism rounded-2xl p-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-lg font-semibold text-white">
            Draft Queue <span className="text-primary-400">({draftCount})</span>
          </h4>
        </div>
        <div className="text-center py-8">
          <div className="text-4xl mb-4">🎵</div>
          <p className="text-dark-300 mb-2">No videos in draft queue</p>
          <p className="text-dark-400 text-sm">Search and add videos to build your queue!</p>
        </div>
      </div>
    )
  }

  return (
    <div className="glassmorphism rounded-2xl p-6">
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-lg font-semibold text-white">
          Draft Queue <span className="text-primary-400">({draftCount})</span>
        </h4>
        <button
          onClick={clearDraft}
          className="btn-secondary text-sm px-3 py-1"
          title="Clear draft queue"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="mr-1">
            <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
          </svg>
          Clear
        </button>
      </div>

      <div className="space-y-3 max-h-80 overflow-y-auto">
        {draftItems.map((draftItem) => (
          <DraftItemEditor
            key={draftItem.draftId}
            item={draftItem}
            onUpdate={(updates) => updateDraftItem(draftItem.draftId, updates)}
            onRemove={() => removeFromDraft(draftItem.draftId)}
          />
        ))}
      </div>
    </div>
  )
}

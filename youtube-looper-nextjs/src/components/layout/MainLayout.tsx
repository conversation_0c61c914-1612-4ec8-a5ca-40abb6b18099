'use client'

import { ReactNode } from 'react'
import { Head<PERSON> } from './Header'
import { Navigation } from './Navigation'
import { Footer } from './Footer'
import { useHeaderMinimize } from './Header'

interface MainLayoutProps {
  children: ReactNode
}

export function MainLayout({ children }: MainLayoutProps) {
  const { isMinimized } = useHeaderMinimize()
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900">
      {/* Header with glassmorphism effect */}
      <Header />
      
      {/* Main content area */}
      <main className="flex-1">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className={`grid grid-cols-1 lg:grid-cols-[80px_1fr] transition-all duration-300 ${
            isMinimized ? 'gap-0' : 'gap-6'
          }`}>
            {/* Compact Navigation Sidebar */}
            <aside className="lg:sticky lg:top-6 lg:h-fit flex justify-center">
              <Navigation />
            </aside>

            {/* Main Content */}
            <div className={`min-h-0 flex flex-col space-y-6 transition-all duration-300 ${
              isMinimized ? 'lg:pl-6' : ''
            }`}>
              {children}
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <Footer />
    </div>
  )
}

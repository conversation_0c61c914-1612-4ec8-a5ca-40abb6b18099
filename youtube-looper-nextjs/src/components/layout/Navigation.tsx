'use client'

import { useNavigation } from '@/hooks/useNavigation'

const navigationItems = [
  {
    id: 'search',
    label: 'Create Queue',
    icon: (
      <svg width="22" height="22" viewBox="0 0 24 24" fill="currentColor">
        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
      </svg>
    ),
  },
  {
    id: 'personal',
    label: 'My Queues',
    icon: (
      <svg width="22" height="22" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
      </svg>
    ),
  },
  {
    id: 'public',
    label: 'Public Queues',
    icon: (
      <svg width="22" height="22" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
      </svg>
    ),
  },
] as const

export function Navigation() {
  const { activeView, setActiveView } = useNavigation()

  return (
    <nav className="glassmorphism rounded-2xl p-2">
      <div className="flex lg:flex-col space-x-2 lg:space-x-0 lg:space-y-2">
        {navigationItems.map((item) => (
          <button
            key={item.id}
            onClick={() => setActiveView(item.id as any)}
            className={`nav-item group relative ${
              activeView === item.id ? 'active' : 'text-dark-300 hover:text-white'
            }`}
          >
            {item.icon}
          </button>
        ))}
      </div>
      
      {/* Mobile labels */}
      <div className="lg:hidden flex justify-center mt-2 space-x-4">
        {navigationItems.map((item) => (
          <span
            key={`${item.id}-label`}
            className={`text-xs transition-colors duration-200 ${
              activeView === item.id ? 'text-primary-400' : 'text-dark-400'
            }`}
          >
            {item.label}
          </span>
        ))}
      </div>
    </nav>
  )
}
